body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.custom-bg {
  background-color: #FDF0D5 ;
}

.text-color {
  color: #780000 ;
}

.text-color1 {
  color: #D4AA00;
}

.border-color {
  border-color: #780000;
}

.custom-bg-text {
  color: #FDF0D5;
}

.bg-color {
  background-color: #780000;
}

.rounded-card {
  padding: 1.5em .5em .5em;
  border-radius: 2em;
  box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

.form-card {
  padding: .2em .2em .2em;
  border-radius: 1em;
  box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

.card {
  transition: transform 0.2s ease;
}

.card:hover .product-card {
  transform: scale(1.02);
}